import { UserProfilePublic } from '@/types/backend';

export interface ProfileCompletionStatus {
  isComplete: boolean;
  completionPercentage: number;
  missingFields: string[];
  requiredFields: string[];
  completedFields: string[];
}

/**
 * Check if a profile is complete and return detailed status
 */
export const checkProfileCompletion = (profile: UserProfilePublic | null): ProfileCompletionStatus => {
  if (!profile) {
    return {
      isComplete: false,
      completionPercentage: 0,
      missingFields: [
        'other_names',
        'surname_name', 
        'date_of_birth',
        'postal_address',
        'verifiable_id',
        'id_type',
        'occupation',
        'copy_of_verifiable_id',
        'proof_of_address',
        'proof_of_income',
        'bank_confirmation_letter',
        'kyc_acknowledgement'
      ],
      requiredFields: [
        'other_names',
        'surname_name', 
        'date_of_birth',
        'postal_address',
        'verifiable_id',
        'id_type',
        'occupation',
        'copy_of_verifiable_id',
        'proof_of_address',
        'proof_of_income',
        'bank_confirmation_letter',
        'kyc_acknowledgement'
      ],
      completedFields: []
    };
  }

  // Define required fields for profile completion
  const requiredFields = [
    'other_names',
    'surname_name', 
    'date_of_birth',
    'postal_address',
    'verifiable_id',
    'id_type',
    'occupation',
    'copy_of_verifiable_id',
    'proof_of_address',
    'proof_of_income',
    'bank_confirmation_letter',
    'kyc_acknowledgement'
  ];

  const completedFields: string[] = [];
  const missingFields: string[] = [];

  // Check each required field
  requiredFields.forEach(field => {
    const value = profile[field as keyof UserProfilePublic];
    
    if (field === 'copy_of_verifiable_id' || 
        field === 'proof_of_address' || 
        field === 'proof_of_income' || 
        field === 'bank_confirmation_letter' || 
        field === 'kyc_acknowledgement') {
      // Boolean fields - must be true
      if (value === true) {
        completedFields.push(field);
      } else {
        missingFields.push(field);
      }
    } else {
      // String fields - must not be empty
      if (value && typeof value === 'string' && value.trim() !== '') {
        completedFields.push(field);
      } else {
        missingFields.push(field);
      }
    }
  });

  const completionPercentage = Math.round((completedFields.length / requiredFields.length) * 100);
  const isComplete = missingFields.length === 0 && profile.status === 'approved';

  return {
    isComplete,
    completionPercentage,
    missingFields,
    requiredFields,
    completedFields
  };
};

/**
 * Get user-friendly field names for display
 */
export const getFieldDisplayName = (fieldName: string): string => {
  const fieldNames: Record<string, string> = {
    other_names: 'First Name',
    surname_name: 'Last Name',
    date_of_birth: 'Date of Birth',
    postal_address: 'Postal Address',
    verifiable_id: 'ID Number',
    id_type: 'ID Type',
    occupation: 'Occupation',
    copy_of_verifiable_id: 'Copy of ID Document',
    proof_of_address: 'Proof of Address',
    proof_of_income: 'Proof of Income',
    bank_confirmation_letter: 'Bank Confirmation Letter',
    kyc_acknowledgement: 'KYC Acknowledgement'
  };

  return fieldNames[fieldName] || fieldName;
};

/**
 * Get completion status message
 */
export const getCompletionMessage = (status: ProfileCompletionStatus): string => {
  if (status.isComplete) {
    return 'Your profile is complete and verified!';
  }

  if (status.completionPercentage === 0) {
    return 'Please complete your profile to access all features.';
  }

  if (status.completionPercentage < 50) {
    return `Your profile is ${status.completionPercentage}% complete. Please add more information.`;
  }

  if (status.completionPercentage < 100) {
    return `Your profile is ${status.completionPercentage}% complete. Just a few more steps!`;
  }

  return 'Your profile is complete but pending verification.';
};

/**
 * Get next steps for profile completion
 */
export const getNextSteps = (status: ProfileCompletionStatus): string[] => {
  if (status.isComplete) {
    return ['Your profile is complete!'];
  }

  const steps: string[] = [];
  
  // Personal information steps
  const personalFields = ['other_names', 'surname_name', 'date_of_birth', 'postal_address', 'verifiable_id', 'id_type', 'occupation'];
  const missingPersonalFields = status.missingFields.filter(field => personalFields.includes(field));
  
  if (missingPersonalFields.length > 0) {
    steps.push(`Complete personal information (${missingPersonalFields.map(getFieldDisplayName).join(', ')})`);
  }

  // Document upload steps
  const documentFields = ['copy_of_verifiable_id', 'proof_of_address', 'proof_of_income', 'bank_confirmation_letter'];
  const missingDocumentFields = status.missingFields.filter(field => documentFields.includes(field));
  
  if (missingDocumentFields.length > 0) {
    steps.push(`Upload required documents (${missingDocumentFields.map(getFieldDisplayName).join(', ')})`);
  }

  // KYC acknowledgement
  if (status.missingFields.includes('kyc_acknowledgement')) {
    steps.push('Complete KYC acknowledgement');
  }

  return steps.length > 0 ? steps : ['Complete your profile'];
};

/**
 * Check if user can access a specific feature
 */
export const canAccessFeature = (
  profile: UserProfilePublic | null, 
  feature: 'quotes' | 'policies' | 'claims' | 'payments' | 'documents'
): { canAccess: boolean; reason?: string } => {
  const status = checkProfileCompletion(profile);

  if (status.isComplete) {
    return { canAccess: true };
  }

  // Different features have different requirements
  switch (feature) {
    case 'quotes':
      // Quotes require at least basic personal information
      const basicFields = ['other_names', 'surname_name', 'date_of_birth', 'postal_address', 'verifiable_id'];
      const hasBasicInfo = basicFields.every(field => !status.missingFields.includes(field));
      
      if (!hasBasicInfo) {
        return { 
          canAccess: false, 
          reason: 'Please complete your basic personal information to get quotes.' 
        };
      }
      return { canAccess: true };

    case 'policies':
      // Policies require complete profile
      return { 
        canAccess: false, 
        reason: 'Please complete your profile and upload all required documents to access policies.' 
      };

    case 'claims':
      // Claims require complete profile
      return { 
        canAccess: false, 
        reason: 'Please complete your profile to submit claims.' 
      };

    case 'payments':
      // Payments require at least basic info
      const hasBasicPaymentInfo = ['other_names', 'surname_name'].every(field => !status.missingFields.includes(field));
      
      if (!hasBasicPaymentInfo) {
        return { 
          canAccess: false, 
          reason: 'Please complete your basic information to access payment features.' 
        };
      }
      return { canAccess: true };

    case 'documents':
      // Documents are always accessible (needed for profile completion)
      return { canAccess: true };

    default:
      return { 
        canAccess: false, 
        reason: 'Please complete your profile to access this feature.' 
      };
  }
};

/**
 * Get profile completion priority (which fields to complete first)
 */
export const getCompletionPriority = (status: ProfileCompletionStatus): string[] => {
  const priority: string[] = [];
  
  // High priority - basic personal info
  const highPriorityFields = ['other_names', 'surname_name', 'date_of_birth'];
  highPriorityFields.forEach(field => {
    if (status.missingFields.includes(field)) {
      priority.push(field);
    }
  });

  // Medium priority - contact and ID info
  const mediumPriorityFields = ['postal_address', 'verifiable_id', 'id_type', 'occupation'];
  mediumPriorityFields.forEach(field => {
    if (status.missingFields.includes(field)) {
      priority.push(field);
    }
  });

  // Low priority - documents
  const lowPriorityFields = ['copy_of_verifiable_id', 'proof_of_address', 'proof_of_income', 'bank_confirmation_letter', 'kyc_acknowledgement'];
  lowPriorityFields.forEach(field => {
    if (status.missingFields.includes(field)) {
      priority.push(field);
    }
  });

  return priority;
};
