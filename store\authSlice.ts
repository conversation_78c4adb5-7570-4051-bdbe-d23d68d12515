import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import * as LocalAuthentication from 'expo-local-authentication';

// Define user types
export type UserType = 'individual' | 'business';

// Base user interface
interface BaseUser {
  id?: string;
  email?: string;
  phone?: string;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  profileImage?: string;
  address?: string;
  userType: UserType;
  role?: 'user' | 'admin' | 'underwriter' | 'support';
}

// Individual user interface
interface IndividualUser extends BaseUser {
  userType: 'individual';
  firstName?: string;
  lastName?: string;
  occupation?: string;
  idNumber?: string;
}

// Business user interface
interface BusinessUser extends BaseUser {
  userType: 'business';
  companyName?: string;
  registrationNumber?: string;
  contactPersonName?: string;
  industry?: string;
  taxNumber?: string;
}

// Combined user type
export type User = IndividualUser | BusinessUser;

// Define auth state
interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isBiometricEnabled: boolean;
  isBiometricAvailable: boolean;
  pendingRegistration: User | null;
  error: string | null;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isLoading: false,
  isAuthenticated: false,
  isBiometricEnabled: false,
  isBiometricAvailable: false,
  pendingRegistration: null,
  error: null,
};

// Async thunks
export const initializeAuth = createAsyncThunk(
  'auth/initialize',
  async (_, { rejectWithValue }) => {
    try {
      // Check biometric availability
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      const isBiometricAvailable = compatible && enrolled;

      // Load biometric preference
      const biometricEnabled = await AsyncStorage.getItem('biometricEnabled');

      // Load user data
      const userJson = await AsyncStorage.getItem('user');
      const token = await AsyncStorage.getItem('token');

      let user = null;
      if (userJson && token) {
        user = JSON.parse(userJson);
        console.log('User loaded from storage:', user);
      }

      return {
        user,
        isBiometricAvailable,
        isBiometricEnabled: biometricEnabled === 'true',
      };
    } catch (error) {
      console.error('Failed to initialize auth:', error);
      return rejectWithValue('Failed to initialize authentication');
    }
  }
);

export const login = createAsyncThunk(
  'auth/login',
  async ({
    email,
    password,
    rememberMe,
    userType = 'individual' // Default to individual login
  }: {
    email: string;
    password: string;
    rememberMe: boolean;
    userType?: UserType;
  }, { rejectWithValue }) => {
    try {
      console.log(`[Auth Slice] Attempting login for ${email} with rememberMe=${rememberMe}, userType=${userType}`);

      // Import API service dynamically to avoid circular dependency
      const { apiService } = await import('@/services/api');

      console.log('[Auth Slice] Calling backend login API');
      // Call the real backend API
      const tokenResponse = await apiService.auth.login(email, password);
      console.log('[Auth Slice] Login API response received:', tokenResponse);

      console.log('[Auth Slice] Getting current user details');
      // Get user details
      const userResponse = await apiService.auth.getCurrentUser();
      console.log('[Auth Slice] Current user response received:', userResponse);

      console.log('[Auth Slice] Storing tokens in AsyncStorage');
      // Store token
      await AsyncStorage.setItem('auth_token', tokenResponse.access_token);
      if (tokenResponse.refresh_token) {
        await AsyncStorage.setItem('refreshToken', tokenResponse.refresh_token);
      }
      console.log('[Auth Slice] Tokens stored successfully');

      // Try to get user profile to check if it's complete
      console.log('[Auth Slice] Checking user profile completion');
      let profileComplete = false;
      try {
        const profileResponse = await apiService.userProfile.getProfile();
        profileComplete = !!(profileResponse && profileResponse.other_names && profileResponse.surname_name);
        console.log('[Auth Slice] Profile found, complete:', profileComplete);
      } catch (profileError) {
        console.log('[Auth Slice] No profile found or error fetching profile:', profileError);
        profileComplete = false;
      }

      console.log('[Auth Slice] Creating user object for userType:', userType);
      // Create user object compatible with existing frontend
      let user: User;

      if (userType === 'individual') {
        user = {
          id: userResponse.id,
          userType: 'individual',
          firstName: userResponse.username?.split(' ')[0] || '',
          lastName: userResponse.username?.split(' ').slice(1).join(' ') || '',
          email: userResponse.email,
          phone: userResponse.phone_number || '',
          address: '',
          isEmailVerified: userResponse.is_email_verified,
          isPhoneVerified: userResponse.is_phone_number_verified,
          role: userResponse.role,
        };
      } else {
        user = {
          id: userResponse.id,
          userType: 'business',
          companyName: userResponse.username || 'Company Name',
          email: userResponse.email,
          phone: userResponse.phone_number || '',
          address: '',
          isEmailVerified: userResponse.is_email_verified,
          isPhoneVerified: userResponse.is_phone_number_verified,
          role: userResponse.role,
        };
      }
      console.log('[Auth Slice] User object created:', user);

      // Store user data if rememberMe is true
      if (rememberMe) {
        console.log('[Auth Slice] Storing user data in AsyncStorage');
        await AsyncStorage.setItem('user', JSON.stringify(user));
      }

      console.log('[Auth Slice] Login successful:', user);
      return user;
    } catch (error) {
      console.error('Login error:', error);
      return rejectWithValue('Login failed');
    }
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (userData: {
    userType: UserType;
    // Individual user fields
    firstName?: string;
    lastName?: string;
    idNumber?: string;
    occupation?: string;
    // Business user fields
    companyName?: string;
    registrationNumber?: string;
    contactPersonName?: string;
    industry?: string;
    taxNumber?: string;
    // Common fields
    email: string;
    phone: string;
    address?: string;
    password: string;
    acceptTerms: boolean;
  }, { rejectWithValue }) => {
    try {
      console.log('Registration data:', userData);

      // Validate terms acceptance
      if (!userData.acceptTerms) {
        return rejectWithValue('Terms and conditions must be accepted');
      }

      // Validate required fields based on user type
      if (userData.userType === 'individual') {
        if (!userData.firstName?.trim()) {
          return rejectWithValue('First name is required');
        }
        if (!userData.lastName?.trim()) {
          return rejectWithValue('Last name is required');
        }
      } else if (userData.userType === 'business') {
        if (!userData.companyName?.trim()) {
          return rejectWithValue('Company name is required');
        }
        if (!userData.registrationNumber?.trim()) {
          return rejectWithValue('Registration number is required');
        }
        if (!userData.contactPersonName?.trim()) {
          return rejectWithValue('Contact person name is required');
        }
      }

      // Validate common fields
      if (!userData.email?.trim()) {
        return rejectWithValue('Email is required');
      }
      if (!userData.phone?.trim()) {
        return rejectWithValue('Phone number is required');
      }

      console.log('[Auth Slice] Starting registration process');
      // Import API service dynamically to avoid circular dependency
      const { apiService } = await import('@/services/api');

      // Create user account
      const userCreateData = {
        email: userData.email,
        hashed_password: userData.password, // Backend will handle proper hashing
        username: userData.userType === 'individual'
          ? `${userData.firstName || ''} ${userData.lastName || ''}`.trim()
          : userData.companyName || '',
        phone_number: userData.phone,
        role: 'user' as const,
      };
      console.log('[Auth Slice] User create data prepared:', userCreateData);

      console.log('[Auth Slice] Calling backend register API');
      const userResponse = await apiService.auth.register(userCreateData);
      console.log('[Auth Slice] Register API response received:', userResponse);

      // After successful registration, automatically log the user in to get auth token
      console.log('[Auth Slice] Auto-login after registration');
      try {
        const loginResponse = await apiService.auth.login(userData.email, userData.password);
        console.log('[Auth Slice] Auto-login successful:', loginResponse);

        // Store tokens
        if (loginResponse.access_token) {
          await AsyncStorage.setItem('auth_token', loginResponse.access_token);
        }
        if (loginResponse.refresh_token) {
          await AsyncStorage.setItem('refresh_token', loginResponse.refresh_token);
        }
        console.log('[Auth Slice] Tokens stored successfully');
      } catch (loginError) {
        console.error('[Auth Slice] Auto-login failed:', loginError);
        // Continue with registration flow even if auto-login fails
      }

      // Create pending user based on user type for OTP verification
      let pendingUser: User;

      if (userData.userType === 'individual') {
        pendingUser = {
          id: userResponse.id,
          userType: 'individual',
          firstName: userData.firstName,
          lastName: userData.lastName,
          idNumber: userData.idNumber,
          occupation: userData.occupation,
          email: userResponse.email,
          phone: userResponse.phone_number || userData.phone,
          address: userData.address,
          isEmailVerified: userResponse.is_email_verified,
          isPhoneVerified: userResponse.is_phone_number_verified,
          role: userResponse.role,
        };
      } else {
        pendingUser = {
          id: userResponse.id,
          userType: 'business',
          companyName: userData.companyName,
          registrationNumber: userData.registrationNumber,
          contactPersonName: userData.contactPersonName,
          industry: userData.industry,
          taxNumber: userData.taxNumber,
          email: userResponse.email,
          phone: userResponse.phone_number || userData.phone,
          address: userData.address,
          isEmailVerified: userResponse.is_email_verified,
          isPhoneVerified: userResponse.is_phone_number_verified,
          role: userResponse.role,
        };
      }

      console.log('Registration successful, awaiting OTP verification');
      return pendingUser;
    } catch (error: any) {
      console.error('Registration error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Registration failed';
      return rejectWithValue(errorMessage);
    }
  }
);

export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ otp, resendOtp = false }: { otp: string; resendOtp?: boolean }, { getState, rejectWithValue }) => {
    try {
      console.log(`Verifying OTP: ${otp}, resend: ${resendOtp}`);

      const state = getState() as { auth: AuthState };
      const pendingRegistration = state.auth.pendingRegistration;

      if (!pendingRegistration) {
        return rejectWithValue('No pending registration found');
      }

      // If this is a resend request, just simulate sending a new OTP
      if (resendOtp) {
        console.log('Resending OTP to', pendingRegistration.email);
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        return pendingRegistration;
      }

      // Validate OTP format (simple validation for demo)
      if (otp.length !== 6 || !/^\d+$/.test(otp)) {
        return rejectWithValue('Invalid OTP format. Please enter a 6-digit code.');
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real app, you would verify the OTP with your backend
      // For now, we'll simulate a successful verification
      const verifiedUser: User = {
        ...pendingRegistration,
        id: 'user-' + Date.now(),
        isEmailVerified: true,
        isPhoneVerified: true,
      };

      await AsyncStorage.setItem('user', JSON.stringify(verifiedUser));
      await AsyncStorage.setItem('token', 'mock-token-' + Date.now());

      console.log('OTP verification successful');
      return verifiedUser;
    } catch (error) {
      console.error('OTP verification error:', error);
      return rejectWithValue('OTP verification failed');
    }
  }
);

export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async (email: string, { rejectWithValue }) => {
    try {
      console.log(`Password reset requested for ${email}`);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real app, you would send a reset link via your backend
      console.log('Password reset email sent');
      return true;
    } catch (error) {
      console.error('Password reset error:', error);
      return rejectWithValue('Password reset failed');
    }
  }
);

export const updatePassword = createAsyncThunk(
  'auth/updatePassword',
  async ({ oldPassword, newPassword }: { oldPassword: string; newPassword: string }, { rejectWithValue }) => {
    try {
      console.log('Updating password');

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real app, you would update the password via your backend
      console.log('Password updated successfully');
      return true;
    } catch (error) {
      console.error('Password update error:', error);
      return rejectWithValue('Password update failed');
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      console.log('Logging out');

      // Clear stored user data
      await AsyncStorage.removeItem('user');
      await AsyncStorage.removeItem('token');

      console.log('Logout successful');
      router.replace('/(auth)/login');
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return rejectWithValue('Logout failed');
    }
  }
);

export const enableBiometric = createAsyncThunk(
  'auth/enableBiometric',
  async (_, { rejectWithValue }) => {
    try {
      // Check biometric hardware availability
      const compatible = await LocalAuthentication.hasHardwareAsync();
      if (!compatible) {
        console.log('Biometric hardware not available');
        return rejectWithValue('Your device doesn\'t support biometric authentication');
      }

      // Check if biometrics are enrolled
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      if (!enrolled) {
        console.log('No biometrics enrolled on device');
        return rejectWithValue('You haven\'t set up biometric authentication on your device');
      }

      // Get available authentication types
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      const isFingerprintSupported = supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT);
      const isFacialSupported = supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION);

      // Customize prompt message based on available authentication types
      let promptMessage = 'Authenticate to enable biometric login';
      if (isFingerprintSupported && !isFacialSupported) {
        promptMessage = 'Scan your fingerprint to enable fingerprint login';
      } else if (isFacialSupported && !isFingerprintSupported) {
        promptMessage = 'Scan your face to enable facial recognition login';
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        fallbackLabel: 'Use passcode',
        disableDeviceFallback: false,
      });

      if (result.success) {
        await AsyncStorage.setItem('biometricEnabled', 'true');
        console.log('Biometric authentication enabled');
        return true;
      } else {
        console.log('Biometric authentication failed or cancelled');
        if (result.error === 'user_cancel') {
          return rejectWithValue('Biometric setup cancelled');
        } else {
          return rejectWithValue('Biometric authentication failed. Please try again.');
        }
      }
    } catch (error) {
      console.error('Enable biometric error:', error);
      return rejectWithValue('Failed to enable biometric authentication');
    }
  }
);

export const loginWithGoogle = createAsyncThunk(
  'auth/loginWithGoogle',
  async (_, { rejectWithValue }) => {
    try {
      console.log('Attempting Google Sign-in');

      // Import API service dynamically to avoid circular dependency
      const { apiService } = await import('@/services/api');

      // Call the Google login endpoint from our API service
      const response = await apiService.auth.googleLogin();

      // The response should contain the auth URL to redirect to
      if (response && response.auth_url) {
        // In a real implementation, we would use WebBrowser.openAuthSessionAsync
        // to handle the OAuth flow and capture the redirect with the token
        console.log('[Auth Slice] Google OAuth URL received:', response.auth_url);

        // TODO: Implement actual OAuth flow using WebBrowser.openAuthSessionAsync
        // For now, return error indicating OAuth flow not implemented
        throw new Error('Google OAuth flow not yet implemented. Please use email/password login.');
      } else {
        throw new Error('Failed to get Google authentication URL');
      }
    } catch (error) {
      console.error('Google Sign-in error:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Google Sign-in failed');
    }
  }
);

export const authenticateWithBiometric = createAsyncThunk(
  'auth/authenticateWithBiometric',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const { isBiometricEnabled } = state.auth;

      // Double-check biometric availability
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      const realBiometricAvailable = compatible && enrolled;

      if (!realBiometricAvailable || !isBiometricEnabled) {
        console.log('Biometric authentication not available or not enabled');
        return rejectWithValue('Biometric authentication not available or not enabled');
      }

      // Get available authentication types
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      const isFingerprintSupported = supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT);
      const isFacialSupported = supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION);

      // Customize prompt message based on available authentication types
      let promptMessage = 'Login with biometrics';
      if (isFingerprintSupported && !isFacialSupported) {
        promptMessage = 'Login with fingerprint';
      } else if (isFacialSupported && !isFingerprintSupported) {
        promptMessage = 'Login with facial recognition';
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        fallbackLabel: 'Use password',
        disableDeviceFallback: false, // Allow device fallback methods
      });

      if (result.success) {
        console.log('Biometric authentication successful');

        // Load user data from storage
        const userJson = await AsyncStorage.getItem('user');
        const token = await AsyncStorage.getItem('token');

        if (userJson && token) {
          const userData = JSON.parse(userJson);
          return userData;
        } else {
          console.log('No stored user data found');
          return rejectWithValue('No stored user data found. Please login with email and password first.');
        }
      } else {
        console.log('Biometric authentication failed or cancelled');
        if (result.error === 'user_cancel') {
          return rejectWithValue('Biometric authentication cancelled');
        } else if (result.error === 'lockout' || result.error === 'authentication_failed') {
          return rejectWithValue('Too many failed attempts. Please use password login.');
        } else {
          return rejectWithValue('Biometric authentication failed or cancelled');
        }
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return rejectWithValue('Biometric authentication error. Please try again or use password login.');
    }
  }
);

export const toggleBiometric = createAsyncThunk(
  'auth/toggleBiometric',
  async (enabled: boolean, { dispatch, rejectWithValue }) => {
    try {
      if (enabled) {
        const result = await dispatch(enableBiometric());
        if (result.meta.requestStatus === 'rejected') {
          return rejectWithValue('Failed to enable biometric authentication');
        }
      } else {
        await AsyncStorage.setItem('biometricEnabled', 'false');
        console.log('Biometric authentication disabled');
      }
      return enabled;
    } catch (error) {
      console.error('Toggle biometric error:', error);
      return rejectWithValue('Failed to toggle biometric authentication');
    }
  }
);

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (profileData: {
    // Individual user fields
    firstName?: string;
    lastName?: string;
    idNumber?: string;
    occupation?: string;
    // Business user fields
    companyName?: string;
    registrationNumber?: string;
    contactPersonName?: string;
    industry?: string;
    taxNumber?: string;
    // Common fields
    email?: string;
    phone?: string;
    address?: string;
  }, { getState, rejectWithValue }) => {
    try {
      console.log('Updating profile with data:', profileData);

      const state = getState() as { auth: AuthState };
      const currentUser = state.auth.user;

      if (!currentUser) {
        return rejectWithValue('No user is currently logged in');
      }

      // Validate required fields based on user type
      if (currentUser.userType === 'individual') {
        if (profileData.firstName === '') {
          return rejectWithValue('First name is required');
        }
        if (profileData.lastName === '') {
          return rejectWithValue('Last name is required');
        }
      } else if (currentUser.userType === 'business') {
        if (profileData.companyName === '') {
          return rejectWithValue('Company name is required');
        }
        if (profileData.registrationNumber === '') {
          return rejectWithValue('Registration number is required');
        }
        if (profileData.contactPersonName === '') {
          return rejectWithValue('Contact person name is required');
        }
      }

      // Validate common fields
      if (profileData.email === '') {
        return rejectWithValue('Email is required');
      }
      if (profileData.phone === '') {
        return rejectWithValue('Phone number is required');
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create updated user object based on user type
      let updatedUser: User;

      if (currentUser.userType === 'individual') {
        updatedUser = {
          ...currentUser,
          ...profileData,
          userType: 'individual',
        } as IndividualUser;
      } else {
        updatedUser = {
          ...currentUser,
          ...profileData,
          userType: 'business',
        } as BusinessUser;
      }

      // Update user in storage
      await AsyncStorage.setItem('user', JSON.stringify(updatedUser));

      console.log('Profile updated successfully:', updatedUser);
      return updatedUser;
    } catch (error) {
      console.error('Profile update error:', error);
      return rejectWithValue('Failed to update profile');
    }
  }
);

export const updateProfileImage = createAsyncThunk(
  'auth/updateProfileImage',
  async (imageUri: string, { getState, rejectWithValue }) => {
    try {
      console.log('Updating profile image:', imageUri);

      const state = getState() as { auth: AuthState };
      const currentUser = state.auth.user;

      if (!currentUser) {
        return rejectWithValue('No user is currently logged in');
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create updated user object with new profile image
      const updatedUser: User = {
        ...currentUser,
        profileImage: imageUri,
      };

      // Update user in storage
      await AsyncStorage.setItem('user', JSON.stringify(updatedUser));

      console.log('Profile image updated successfully');
      return updatedUser;
    } catch (error) {
      console.error('Profile image update error:', error);
      return rejectWithValue('Failed to update profile image');
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setPendingRegistration: (state, action: PayloadAction<User | null>) => {
      state.pendingRegistration = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Initialize auth
    builder.addCase(initializeAuth.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(initializeAuth.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload.user;
      state.isAuthenticated = !!action.payload.user;
      state.isBiometricAvailable = action.payload.isBiometricAvailable;
      state.isBiometricEnabled = action.payload.isBiometricEnabled;
    });
    builder.addCase(initializeAuth.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Login
    builder.addCase(login.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(login.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
    });
    builder.addCase(login.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Register
    builder.addCase(register.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(register.fulfilled, (state, action) => {
      state.isLoading = false;
      state.pendingRegistration = action.payload;
    });
    builder.addCase(register.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Verify OTP
    builder.addCase(verifyOTP.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(verifyOTP.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
      state.pendingRegistration = null;
    });
    builder.addCase(verifyOTP.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Reset Password
    builder.addCase(resetPassword.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(resetPassword.fulfilled, (state) => {
      state.isLoading = false;
    });
    builder.addCase(resetPassword.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Update Password
    builder.addCase(updatePassword.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(updatePassword.fulfilled, (state) => {
      state.isLoading = false;
    });
    builder.addCase(updatePassword.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Logout
    builder.addCase(logout.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(logout.fulfilled, (state) => {
      state.isLoading = false;
      state.user = null;
      state.isAuthenticated = false;
    });
    builder.addCase(logout.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Enable Biometric
    builder.addCase(enableBiometric.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(enableBiometric.fulfilled, (state) => {
      state.isLoading = false;
      state.isBiometricEnabled = true;
    });
    builder.addCase(enableBiometric.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Authenticate with Biometric
    builder.addCase(authenticateWithBiometric.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(authenticateWithBiometric.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload;
      state.isAuthenticated = true;
    });
    builder.addCase(authenticateWithBiometric.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Toggle Biometric
    builder.addCase(toggleBiometric.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(toggleBiometric.fulfilled, (state, action) => {
      state.isLoading = false;
      state.isBiometricEnabled = action.payload;
    });
    builder.addCase(toggleBiometric.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Update Profile
    builder.addCase(updateProfile.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(updateProfile.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload;
    });
    builder.addCase(updateProfile.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Update Profile Image
    builder.addCase(updateProfileImage.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(updateProfileImage.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload;
    });
    builder.addCase(updateProfileImage.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Google Sign-in
    builder.addCase(loginWithGoogle.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(loginWithGoogle.fulfilled, (state) => {
      state.isLoading = false;
      // Google login is not implemented yet, so this won't be called
      // When implemented, action.payload will be a User object
    });
    builder.addCase(loginWithGoogle.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
  },
});

export const { setPendingRegistration, clearError } = authSlice.actions;
export default authSlice.reducer;
