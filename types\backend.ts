// Backend API Types based on OpenAPI schema

// Enums
export enum PolicyType {
  MOTOR = 'motor',
  HOUSE_OWNER = 'house_owner',
  HOUSEHOLD_CONTENTS = 'household_contents',
  ALL_RISKS_SPECIFIED = 'all_risks_specified',
  ALL_RISKS_UNSPECIFIED = 'all_risks_unspecified',
  LIFE_ASSURANCE = 'life_assurance'
}

export enum PolicyProvider {
  HOLLAND = 'holland',
  BOTSWANA_LIFE = 'botswana_life'
}

export enum PolicyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export enum Status {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export enum ProfileStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export enum ConstructionType {
  BRICK = 'brick',
  THATCHED = 'thatched'
}

export enum SecurityType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  PREMIUM = 'premium'
}

export enum Use {
  PRIVATE = 'private',
  COMMERCIAL = 'commercial'
}

export enum IDType {
  NATIONAL = 'national',
  PASSPORT = 'passport'
}

// Base interfaces
export interface InventoryItem {
  item: string;
  value: number;
}

// Policy Details Types
export interface MotorPolicyDetails {
  make: string;
  model: string;
  year: number;
  engine_number: string;
  chassis_number: string;
  registration_number: string;
  current_mileage: number;
  use: Use;
  driver_license?: boolean;
  vehicle_registration_book?: boolean;
  vehicle_valuation_report?: boolean;
  previous_insurance_policy?: boolean;
}

export interface HouseOwnerPolicyDetails {
  property_value: number;
  year_of_construction: number;
  construction_type: ConstructionType;
  is_occupied: boolean;
  title_deed: boolean;
  property_valuation_report: boolean;
}

export interface HouseholdContentsPolicyDetails {
  type_of_security: SecurityType;
  inventory_list: InventoryItem[];
  power_surge_extension: boolean;
  accidental_breakage_extension: boolean;
  inventory_form: boolean;
}

export interface AllRisksSpecifiedItemsPolicyDetails {
  items: InventoryItem[];
}

export interface AllRisksUnspecifiedItemsPolicyDetails {
  total_sum_insured: number;
  items: InventoryItem[];
}

export type PolicyDetails =
  | MotorPolicyDetails
  | HouseOwnerPolicyDetails
  | HouseholdContentsPolicyDetails
  | AllRisksSpecifiedItemsPolicyDetails
  | AllRisksUnspecifiedItemsPolicyDetails;

// User Types
export interface UserCreate {
  email: string;
  hashed_password: string;
  username?: string;
  phone_number?: string;
  role?: 'user' | 'admin' | 'underwriter' | 'support';
}

export interface UserUpdate {
  email?: string;
  hashed_password?: string;
  username?: string;
  phone_number?: string;
  role?: 'user' | 'admin' | 'underwriter' | 'support';
  is_phone_number_verified?: boolean;
  is_email_verified?: boolean;
}

export interface UserPublic {
  id: string;
  email: string;
  username?: string;
  phone_number?: string;
  role: 'user' | 'admin' | 'underwriter' | 'support';
  created_at: string;
  updated_at: string;
  is_phone_number_verified: boolean;
  is_email_verified: boolean;
}

// User Profile Types
export interface UserProfileCreate {
  other_names: string;
  surname_name: string;
  date_of_birth: string;
  postal_address: string;
  verifiable_id: string;
  id_type: IDType;
  occupation: string;
  status?: ProfileStatus;
  copy_of_verifiable_id?: boolean;
  proof_of_address?: boolean;
  proof_of_income?: boolean;
  bank_confirmation_letter?: boolean;
  kyc_acknowledgement?: boolean;
}

export interface UserProfileUpdate {
  other_names?: string;
  surname_name?: string;
  date_of_birth?: string;
  postal_address?: string;
  verifiable_id?: string;
  id_type?: IDType;
  occupation?: string;
  status?: ProfileStatus;
  copy_of_verifiable_id?: boolean;
  proof_of_address?: boolean;
  proof_of_income?: boolean;
  bank_confirmation_letter?: boolean;
  kyc_acknowledgement?: boolean;
}

export interface UserProfilePublic {
  id: string;
  user_id: string;
  other_names: string;
  surname_name: string;
  date_of_birth: string;
  postal_address: string;
  verifiable_id: string;
  id_type: IDType;
  occupation: string;
  status: ProfileStatus;
  copy_of_verifiable_id: boolean;
  proof_of_address: boolean;
  proof_of_income: boolean;
  bank_confirmation_letter: boolean;
  kyc_acknowledgement: boolean;
  reviewer_id?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

// Policy Types
export interface PolicyCreate {
  policy_type?: PolicyType;
  policy_provider?: PolicyProvider;
  status?: PolicyStatus;
  start_date: string;
  end_date: string;
  policy_details: PolicyDetails;
  user_id: string;
}

export interface PolicyUpdate {
  policy_type?: PolicyType;
  policy_provider?: PolicyProvider;
  status?: PolicyStatus;
  start_date?: string;
  end_date?: string;
  policy_details?: PolicyDetails;
}

export interface PolicyPublic {
  id: string;
  user_id: string;
  policy_type: PolicyType;
  policy_provider: PolicyProvider;
  status: PolicyStatus;
  start_date: string;
  end_date: string;
  policy_details: PolicyDetails;
  reviewer_id?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

// Document Types
export interface DocumentPublic {
  id: string;
  user_id: string;
  policy_id?: string;
  name: string;
  status: Status;
  reviewer_id?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentUpdate {
  status: Status;
}

// Auth Types
export interface Token {
  access_token: string;
  token_type: string;
  expires_in?: number;
  refresh_token?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  grant_type?: string;
  scope?: string;
  client_id?: string;
  client_secret?: string;
}

// Error Types
export interface ValidationError {
  loc: (string | number)[];
  msg: string;
  type: string;
}

export interface HTTPValidationError {
  detail: ValidationError[];
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}
