/**
 * Claim Service
 *
 * This service handles API interactions for claims management.
 * Provides both API and mock data implementations.
 */

import { Claim, ClaimInput, ClaimStatus, ClaimTimelineEvent, ClaimType, REQUIRED_DOCUMENTS_BY_CLAIM_TYPE } from '@/types/claim.types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService from './api';
import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import { showToast } from '@/utils/toast';



// Helper function to generate a reference number
const generateReference = (): string => {
  return `CLM-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`;
};

// Helper function to create initial timeline events
const createInitialTimeline = (claimType: ClaimType): ClaimTimelineEvent[] => {
  const currentDate = new Date().toISOString().split('T')[0];

  return [
    {
      id: '1',
      date: currentDate,
      title: 'Claim Created',
      description: 'You have started a new claim.',
      status: 'completed',
      icon: 'file-plus'
    },
    {
      id: '2',
      date: '',
      title: 'Claim Submission',
      description: 'Submit your claim with all required documents.',
      status: 'current',
      icon: 'upload',
      actions: [
        {
          label: 'Complete Submission',
          action: 'submit_claim'
        }
      ]
    },
    {
      id: '3',
      date: '',
      title: 'Claim Review',
      description: 'Your claim will be reviewed by our team.',
      status: 'upcoming',
      icon: 'clipboard'
    },
    {
      id: '4',
      date: '',
      title: 'Assessment',
      description: 'An assessor may be assigned to evaluate your claim.',
      status: 'upcoming',
      icon: 'search'
    },
    {
      id: '5',
      date: '',
      title: 'Decision',
      description: 'A decision will be made on your claim.',
      status: 'upcoming',
      icon: 'check-circle'
    },
    {
      id: '6',
      date: '',
      title: 'Payment',
      description: 'If approved, payment will be processed.',
      status: 'upcoming',
      icon: 'credit-card'
    }
  ];
};

// Helper function to create required documents
const createRequiredDocuments = (claimType: ClaimType) => {
  const currentDate = new Date().toISOString().split('T')[0];
  const requiredDocs = REQUIRED_DOCUMENTS_BY_CLAIM_TYPE[claimType] || [];

  return requiredDocs.map((doc, index) => ({
    id: `doc-${Date.now()}-${index}`,
    name: doc.name,
    type: doc.type,
    status: 'pending' as const,
    date: currentDate,
    required: doc.required
  }));
};



// Claim service methods
export const claimService = {
  // Get all claims
  getAllClaims: async (): Promise<Claim[]> => {
    try {
      console.log('[ClaimService] Getting all claims from API');
      const response = await apiService.claims.getClaims();
      console.log('[ClaimService] Claims retrieved successfully:', response.length);
      return response;
    } catch (error) {
      console.error('[ClaimService] Error getting claims:', error);
      throw error;
    }
  },

  // Get claim by ID
  getClaimById: async (id: string): Promise<Claim | undefined> => {
    try {
      console.log('[ClaimService] Getting claim by ID:', id);
      const response = await apiService.claims.getClaimById(id);
      console.log('[ClaimService] Claim retrieved successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error getting claim with ID:', id, error);
      throw error;
    }
  },

  // Create a new claim
  createClaim: async (claimInput: ClaimInput): Promise<Claim> => {
    try {
      console.log('[ClaimService] Creating new claim:', claimInput);
      const response = await apiService.claims.createClaim(claimInput);
      console.log('[ClaimService] Claim created successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error creating claim:', error);
      throw error;
    }
  },

  // Update claim
  updateClaim: async (id: string, updates: Partial<Claim>): Promise<Claim> => {
    try {
      console.log('[ClaimService] Updating claim:', id, updates);
      const response = await apiService.claims.updateClaim(id, updates);
      console.log('[ClaimService] Claim updated successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error updating claim:', id, error);
      throw error;
    }
  },

  // Submit claim
  submitClaim: async (id: string): Promise<Claim> => {
    try {
      console.log('[ClaimService] Submitting claim:', id);
      const response = await apiService.claims.submitClaim(id);
      console.log('[ClaimService] Claim submitted successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error submitting claim:', id, error);
      throw error;
    }
  },

  // Update claim status
  updateClaimStatus: async (id: string, status: ClaimStatus): Promise<Claim> => {
    try {
      console.log('[ClaimService] Updating claim status:', id, status);
      const response = await apiService.claims.updateClaim(id, { status });
      console.log('[ClaimService] Claim status updated successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error updating claim status:', id, error);
      throw error;
    }
  },

  // Upload a document for a claim
  uploadClaimDocument: async (claimId: string, file: any, documentType: string) => {
    try {
      console.log('[ClaimService] Uploading document for claim:', claimId);
      const response = await apiService.claims.uploadDocument(claimId, file, documentType);
      console.log('[ClaimService] Document uploaded successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error uploading document for claim:', claimId, error);
      throw error;
    }
  },

  // Get claim documents
  getClaimDocuments: async (claimId: string) => {
    try {
      console.log('[ClaimService] Getting documents for claim:', claimId);
      const response = await apiService.claims.getClaimDocuments(claimId);
      console.log('[ClaimService] Documents retrieved successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error getting claim documents:', claimId, error);
      throw error;
    }
  },

  // Get claim timeline
  getClaimTimeline: async (claimId: string) => {
    try {
      console.log('[ClaimService] Getting timeline for claim:', claimId);
      const response = await apiService.claims.getClaimTimeline(claimId);
      console.log('[ClaimService] Timeline retrieved successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error getting claim timeline:', claimId, error);
      throw error;
    }
  },

  // Withdraw claim
  withdrawClaim: async (claimId: string, reason: string) => {
    try {
      console.log('[ClaimService] Withdrawing claim:', claimId, reason);
      const response = await apiService.claims.withdrawClaim(claimId, reason);
      console.log('[ClaimService] Claim withdrawn successfully');
      return response;
    } catch (error) {
      console.error('[ClaimService] Error withdrawing claim:', claimId, error);
      throw error;
    }
  },



  // Pick a document for a claim
  pickClaimDocument: async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['image/*', 'application/pdf'],
        copyToCacheDirectory: true
      });

      if (result.canceled) {
        throw new Error('Document picking canceled');
      }

      const asset = result.assets?.[0];
      if (!asset) {
        throw new Error('No document selected');
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(asset.uri);
      if (!fileInfo.exists) {
        throw new Error('File does not exist');
      }

      // Check file size (limit to 10MB)
      if (fileInfo.size && fileInfo.size > 10 * 1024 * 1024) {
        showToast(
          'error',
          'File Too Large',
          'Please select a file smaller than 10MB',
          { visibilityTime: 3000 }
        );
        throw new Error('File too large');
      }

      return {
        uri: asset.uri,
        name: asset.name || 'claim_document.pdf',
        type: asset.mimeType || 'application/pdf',
        size: fileInfo.size
      };
    } catch (error) {
      console.error('Error picking claim document:', error);
      throw error;
    }
  }
};

export default claimService;
