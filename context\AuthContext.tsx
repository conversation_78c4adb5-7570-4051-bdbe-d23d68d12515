import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';
import * as LocalAuthentication from 'expo-local-authentication';
import { router } from 'expo-router';
import Constants from 'expo-constants';

// Ensure the browser redirects correctly
WebBrowser.maybeCompleteAuthSession();

// Define user type
export type User = {
  id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  profileImage?: string;
};

// Define auth context type
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isBiometricEnabled: boolean;
  isBiometricAvailable: boolean;
  login: (email: string, password: string, rememberMe: boolean) => Promise<boolean>;
  loginWithGoogle: () => Promise<void>;
  register: (userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    password: string;
    acceptTerms: boolean;
  }) => Promise<boolean>;
  verifyOTP: (otp: string) => Promise<boolean>;
  resetPassword: (email: string) => Promise<boolean>;
  updatePassword: (oldPassword: string, newPassword: string) => Promise<boolean>;
  logout: () => Promise<void>;
  enableBiometric: () => Promise<boolean>;
  authenticateWithBiometric: () => Promise<boolean>;
  toggleBiometric: (enabled: boolean) => Promise<void>;
}

// Create the auth context
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  isBiometricEnabled: false,
  isBiometricAvailable: false,
  login: async () => false,
  loginWithGoogle: async () => {},
  register: async () => false,
  verifyOTP: async () => false,
  resetPassword: async () => false,
  updatePassword: async () => false,
  logout: async () => {},
  enableBiometric: async () => false,
  authenticateWithBiometric: async () => false,
  toggleBiometric: async () => {},
});

// Google OAuth client IDs - replace with your actual client IDs
const GOOGLE_CLIENT_ID = Constants.expoConfig?.extra?.googleClientId || 'YOUR_GOOGLE_CLIENT_ID';
const GOOGLE_ANDROID_CLIENT_ID = Constants.expoConfig?.extra?.googleAndroidClientId || 'YOUR_ANDROID_CLIENT_ID';
const GOOGLE_IOS_CLIENT_ID = Constants.expoConfig?.extra?.googleIosClientId || 'YOUR_IOS_CLIENT_ID';

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);
  const [isBiometricAvailable, setIsBiometricAvailable] = useState(false);


  // Google OAuth setup
  const [request, response, promptAsync] = Google.useAuthRequest({
    expoClientId: GOOGLE_CLIENT_ID,
    androidClientId: GOOGLE_ANDROID_CLIENT_ID,
    iosClientId: GOOGLE_IOS_CLIENT_ID,
    webClientId: GOOGLE_CLIENT_ID,
  });

  // Check biometric availability
  useEffect(() => {
    const checkBiometrics = async () => {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      setIsBiometricAvailable(compatible && enrolled);

      // Load biometric preference
      try {
        const biometricEnabled = await AsyncStorage.getItem('biometricEnabled');
        setIsBiometricEnabled(biometricEnabled === 'true');
      } catch (error) {
        console.error('Failed to load biometric preference:', error);
      }
    };

    checkBiometrics();
  }, []);

  // Load user from storage on app start
  useEffect(() => {
    const loadUser = async () => {
      try {
        const userJson = await AsyncStorage.getItem('user');
        const token = await AsyncStorage.getItem('token');

        if (userJson && token) {
          const userData = JSON.parse(userJson);
          setUser(userData);
          console.log('User loaded from storage:', userData);
        }
      } catch (error) {
        console.error('Failed to load user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Handle Google auth response
  useEffect(() => {
    if (response?.type === 'success') {
      const { authentication } = response;
      if (authentication?.accessToken) {
        handleGoogleLogin(authentication.accessToken);
      }
    }
  }, [response]);

  // Handle Google login
  const handleGoogleLogin = async (accessToken: string) => {
    try {
      console.log('Google login successful, token:', accessToken);

      // In a real app, you would send this token to your backend
      // For now, we'll create an empty user object that will be populated by user input
      const emptyUser: User = {
        id: 'google-user-' + Date.now(),
        isEmailVerified: true,
      };

      await AsyncStorage.setItem('user', JSON.stringify(emptyUser));
      await AsyncStorage.setItem('token', 'token-' + Date.now());

      setUser(emptyUser);
      router.replace('/(app)/(tabs)');
    } catch (error) {
      console.error('Google login error:', error);
    }
  };

  // Login function - now delegates to Redux auth slice
  const login = async (_email: string, _password: string, _rememberMe: boolean): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('[AuthContext] Login delegated to Redux auth slice');

      // This context is now deprecated in favor of Redux auth slice
      // The actual login logic is handled in store/authSlice.ts
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Google login function
  const loginWithGoogle = async (): Promise<void> => {
    console.log('Initiating Google login');
    await promptAsync();
  };

  // Register function - now delegates to Redux auth slice
  const register = async (_userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    password: string;
    acceptTerms: boolean;
  }): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('[AuthContext] Registration delegated to Redux auth slice');

      // This context is now deprecated in favor of Redux auth slice
      // The actual registration logic is handled in store/authSlice.ts
      return false;
    } catch (error) {
      console.error('Registration error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // OTP verification function - now delegates to Redux auth slice
  const verifyOTP = async (_otp: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('[AuthContext] OTP verification delegated to Redux auth slice');

      // This context is now deprecated in favor of Redux auth slice
      // The actual OTP verification logic is handled in store/authSlice.ts
      return false;
    } catch (error) {
      console.error('OTP verification error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset password function - now delegates to Redux auth slice
  const resetPassword = async (_email: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('[AuthContext] Password reset delegated to Redux auth slice');

      // This context is now deprecated in favor of Redux auth slice
      return false;
    } catch (error) {
      console.error('Password reset error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Update password function - now delegates to Redux auth slice
  const updatePassword = async (_oldPassword: string, _newPassword: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('[AuthContext] Password update delegated to Redux auth slice');

      // This context is now deprecated in favor of Redux auth slice
      return false;
    } catch (error) {
      console.error('Password update error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      console.log('Logging out');

      // Clear stored user data
      await AsyncStorage.removeItem('user');
      await AsyncStorage.removeItem('token');

      setUser(null);
      console.log('Logout successful');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Enable biometric authentication
  const enableBiometric = async (): Promise<boolean> => {
    try {
      if (!isBiometricAvailable) {
        console.log('Biometric authentication not available');
        return false;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to enable biometric login',
        fallbackLabel: 'Use passcode',
      });

      if (result.success) {
        await AsyncStorage.setItem('biometricEnabled', 'true');
        setIsBiometricEnabled(true);
        console.log('Biometric authentication enabled');
        return true;
      } else {
        console.log('Biometric authentication failed or cancelled');
        return false;
      }
    } catch (error) {
      console.error('Enable biometric error:', error);
      return false;
    }
  };

  // Authenticate with biometric
  const authenticateWithBiometric = async (): Promise<boolean> => {
    try {
      if (!isBiometricAvailable || !isBiometricEnabled) {
        console.log('Biometric authentication not available or not enabled');
        return false;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Login with biometrics',
        fallbackLabel: 'Use password',
      });

      if (result.success) {
        console.log('Biometric authentication successful');

        // Load user data from storage
        const userJson = await AsyncStorage.getItem('user');
        const token = await AsyncStorage.getItem('token');

        if (userJson && token) {
          const userData = JSON.parse(userJson);
          setUser(userData);
          return true;
        } else {
          console.log('No stored user data found');
          return false;
        }
      } else {
        console.log('Biometric authentication failed or cancelled');
        return false;
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  };

  // Toggle biometric authentication
  const toggleBiometric = async (enabled: boolean): Promise<void> => {
    try {
      if (enabled) {
        const success = await enableBiometric();
        if (!success) {
          return;
        }
      } else {
        await AsyncStorage.setItem('biometricEnabled', 'false');
        setIsBiometricEnabled(false);
        console.log('Biometric authentication disabled');
      }
    } catch (error) {
      console.error('Toggle biometric error:', error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        isBiometricEnabled,
        isBiometricAvailable,
        login,
        loginWithGoogle,
        register,
        verifyOTP,
        resetPassword,
        updatePassword,
        logout,
        enableBiometric,
        authenticateWithBiometric,
        toggleBiometric,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => useContext(AuthContext);

