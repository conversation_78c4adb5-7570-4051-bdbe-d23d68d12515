import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import useNotificationStore from '@/store/notificationStore';
import { showToast } from '@/utils/toast';

const NotificationDemo: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const {
    notifyDocumentVerified,
    notifyDocumentRejected,
    notifyApplicationStatusChange,
    notifyPaymentDue,
    notifyPaymentReceived,
    notifyPolicyIssued,
    notifyClaimUpdate,
    notifyPromotion,
    notifications,
    unreadCount,
  } = useNotificationStore();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
      backgroundColor: colors.background,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 12,
    },
    button: {
      backgroundColor: colors.primary,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      alignItems: 'center',
    },
    buttonText: {
      color: colors.background,
      fontWeight: '600',
    },
    statsContainer: {
      backgroundColor: colors.surface,
      padding: 16,
      borderRadius: 12,
      marginBottom: 20,
    },
    statsText: {
      color: colors.text,
      fontSize: 16,
      textAlign: 'center',
    },
    warningButton: {
      backgroundColor: colors.warning,
    },
    errorButton: {
      backgroundColor: colors.error,
    },
    successButton: {
      backgroundColor: colors.success,
    },
  });

  const handleDocumentVerified = async () => {
    await notifyDocumentVerified('Driver\'s License', 'doc-123');
    showToast('success', 'Demo', 'Document verification notification sent!');
  };

  const handleDocumentRejected = async () => {
    await notifyDocumentRejected('Passport Copy', 'doc-456', 'Image quality too low');
    showToast('error', 'Demo', 'Document rejection notification sent!');
  };

  const handleApplicationApproved = async () => {
    await notifyApplicationStatusChange('app-789', 'approved', 'Your motor insurance application has been approved!');
    showToast('success', 'Demo', 'Application approval notification sent!');
  };

  const handleApplicationRejected = async () => {
    await notifyApplicationStatusChange('app-101', 'rejected', 'Your application was rejected due to incomplete documentation.');
    showToast('error', 'Demo', 'Application rejection notification sent!');
  };

  const handlePaymentDue = async () => {
    await notifyPaymentDue('POL-2024-001', 1500.00, '2024-06-15');
    showToast('warning', 'Demo', 'Payment due notification sent!');
  };

  const handlePaymentReceived = async () => {
    await notifyPaymentReceived('POL-2024-001', 1500.00);
    showToast('success', 'Demo', 'Payment received notification sent!');
  };

  const handlePolicyIssued = async () => {
    await notifyPolicyIssued('POL-2024-002', 'Motor Insurance');
    showToast('success', 'Demo', 'Policy issued notification sent!');
  };

  const handleClaimApproved = async () => {
    await notifyClaimUpdate('CLM-2024-001', 'approved', 'Your claim has been approved for BWP 5,000.00');
    showToast('success', 'Demo', 'Claim approval notification sent!');
  };

  const handleClaimRejected = async () => {
    await notifyClaimUpdate('CLM-2024-002', 'rejected', 'Your claim was rejected due to policy exclusions.');
    showToast('error', 'Demo', 'Claim rejection notification sent!');
  };

  const handlePromotion = async () => {
    await notifyPromotion(
      'Special Offer: 20% Off Motor Insurance',
      'Get 20% off your next motor insurance policy. Limited time offer!',
      '/promotions/motor-20-off'
    );
    showToast('info', 'Demo', 'Promotion notification sent!');
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Text style={styles.title}>Notification System Demo</Text>
      
      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          Total Notifications: {notifications.length} | Unread: {unreadCount}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Document Notifications</Text>
        <TouchableOpacity style={[styles.button, styles.successButton]} onPress={handleDocumentVerified}>
          <Text style={styles.buttonText}>Document Verified ✅</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.errorButton]} onPress={handleDocumentRejected}>
          <Text style={styles.buttonText}>Document Rejected ❌</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Application Notifications</Text>
        <TouchableOpacity style={[styles.button, styles.successButton]} onPress={handleApplicationApproved}>
          <Text style={styles.buttonText}>Application Approved ✅</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.errorButton]} onPress={handleApplicationRejected}>
          <Text style={styles.buttonText}>Application Rejected ❌</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Payment Notifications</Text>
        <TouchableOpacity style={[styles.button, styles.warningButton]} onPress={handlePaymentDue}>
          <Text style={styles.buttonText}>Payment Due 💰</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.successButton]} onPress={handlePaymentReceived}>
          <Text style={styles.buttonText}>Payment Received ✅</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Policy Notifications</Text>
        <TouchableOpacity style={[styles.button, styles.successButton]} onPress={handlePolicyIssued}>
          <Text style={styles.buttonText}>Policy Issued 🎉</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Claim Notifications</Text>
        <TouchableOpacity style={[styles.button, styles.successButton]} onPress={handleClaimApproved}>
          <Text style={styles.buttonText}>Claim Approved ✅</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.errorButton]} onPress={handleClaimRejected}>
          <Text style={styles.buttonText}>Claim Rejected ❌</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Promotional Notifications</Text>
        <TouchableOpacity style={styles.button} onPress={handlePromotion}>
          <Text style={styles.buttonText}>Send Promotion 🎁</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default NotificationDemo;
