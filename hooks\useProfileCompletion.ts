import { useEffect, useState } from 'react';
import { useAppSelector } from '@/store/hooks';
import { checkProfileCompletion, canAccessFeature, ProfileCompletionStatus } from '@/utils/profileCompletion';
import { UserProfilePublic } from '@/types/backend';

export interface UseProfileCompletionReturn {
  profile: UserProfilePublic | null;
  completionStatus: ProfileCompletionStatus;
  isProfileComplete: boolean;
  completionPercentage: number;
  canAccessQuotes: boolean;
  canAccessPolicies: boolean;
  canAccessClaims: boolean;
  canAccessPayments: boolean;
  canAccessDocuments: boolean;
  checkFeatureAccess: (feature: 'quotes' | 'policies' | 'claims' | 'payments' | 'documents') => {
    canAccess: boolean;
    reason?: string;
  };
  isLoading: boolean;
}

/**
 * Hook to check profile completion status and feature access
 */
export const useProfileCompletion = (): UseProfileCompletionReturn => {
  const profile = useAppSelector(state => state.profile.profile);
  const isLoading = useAppSelector(state => state.profile.isLoading);
  
  const [completionStatus, setCompletionStatus] = useState<ProfileCompletionStatus>({
    isComplete: false,
    completionPercentage: 0,
    missingFields: [],
    requiredFields: [],
    completedFields: []
  });

  // Update completion status when profile changes
  useEffect(() => {
    const status = checkProfileCompletion(profile);
    setCompletionStatus(status);
  }, [profile]);

  // Check feature access
  const checkFeatureAccess = (feature: 'quotes' | 'policies' | 'claims' | 'payments' | 'documents') => {
    return canAccessFeature(profile, feature);
  };

  // Pre-calculate common feature access
  const canAccessQuotes = canAccessFeature(profile, 'quotes').canAccess;
  const canAccessPolicies = canAccessFeature(profile, 'policies').canAccess;
  const canAccessClaims = canAccessFeature(profile, 'claims').canAccess;
  const canAccessPayments = canAccessFeature(profile, 'payments').canAccess;
  const canAccessDocuments = canAccessFeature(profile, 'documents').canAccess;

  return {
    profile,
    completionStatus,
    isProfileComplete: completionStatus.isComplete,
    completionPercentage: completionStatus.completionPercentage,
    canAccessQuotes,
    canAccessPolicies,
    canAccessClaims,
    canAccessPayments,
    canAccessDocuments,
    checkFeatureAccess,
    isLoading
  };
};
