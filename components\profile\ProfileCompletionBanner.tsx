import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { useProfileCompletion } from '@/hooks/useProfileCompletion';
import { getCompletionMessage, getNextSteps } from '@/utils/profileCompletion';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ProfileCompletionBannerProps {
  showOnComplete?: boolean;
  dismissible?: boolean;
}

const ProfileCompletionBanner: React.FC<ProfileCompletionBannerProps> = ({
  showOnComplete = false,
  dismissible = true
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const { completionStatus, isProfileComplete, completionPercentage } = useProfileCompletion();
  
  const [isDismissed, setIsDismissed] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(1));

  // Don't show banner if profile is complete and showOnComplete is false
  if (isProfileComplete && !showOnComplete) {
    return null;
  }

  // Don't show if dismissed
  if (isDismissed) {
    return null;
  }

  const handleDismiss = async () => {
    if (!dismissible) return;

    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsDismissed(true);
    });

    // Store dismissal state (but allow it to reappear if profile becomes more incomplete)
    try {
      await AsyncStorage.setItem('profileBannerDismissed', JSON.stringify({
        dismissed: true,
        completionPercentage,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('Error storing banner dismissal:', error);
    }
  };

  const handleCompleteProfile = () => {
    router.push('/(app)/profile/edit');
  };

  const getBannerColor = () => {
    if (isProfileComplete) {
      return colors.success;
    } else if (completionPercentage >= 75) {
      return colors.warning;
    } else if (completionPercentage >= 50) {
      return '#FF8C42'; // Orange
    } else {
      return colors.error;
    }
  };

  const getBannerIcon = () => {
    if (isProfileComplete) {
      return 'checkmark-circle';
    } else if (completionPercentage >= 75) {
      return 'time';
    } else {
      return 'alert-circle';
    }
  };

  const message = getCompletionMessage(completionStatus);
  const nextSteps = getNextSteps(completionStatus);

  const styles = StyleSheet.create({
    container: {
      backgroundColor: getBannerColor(),
      marginHorizontal: spacing.md,
      marginVertical: spacing.sm,
      borderRadius: 12,
      padding: spacing.md,
      flexDirection: 'row',
      alignItems: 'flex-start',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    iconContainer: {
      marginRight: spacing.sm,
      marginTop: 2,
    },
    icon: {
      color: colors.background,
    },
    content: {
      flex: 1,
    },
    title: {
      fontFamily: typography.fonts.semiBold,
      fontSize: typography.sizes.md,
      color: colors.background,
      marginBottom: spacing.xs,
    },
    message: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.background,
      marginBottom: spacing.sm,
      lineHeight: 20,
    },
    progressContainer: {
      marginBottom: spacing.sm,
    },
    progressBar: {
      height: 6,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      borderRadius: 3,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: colors.background,
      borderRadius: 3,
    },
    progressText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.background,
      marginTop: spacing.xs,
    },
    nextStepsContainer: {
      marginBottom: spacing.sm,
    },
    nextStepText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.background,
      marginBottom: 2,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    completeButton: {
      backgroundColor: colors.background,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
    },
    completeButtonText: {
      fontFamily: typography.fonts.semiBold,
      fontSize: typography.sizes.sm,
      color: getBannerColor(),
      marginRight: spacing.xs,
    },
    dismissButton: {
      padding: spacing.xs,
    },
    dismissIcon: {
      color: colors.background,
    },
  });

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.iconContainer}>
        <Ionicons 
          name={getBannerIcon()} 
          size={24} 
          style={styles.icon}
        />
      </View>
      
      <View style={styles.content}>
        <Text style={styles.title}>
          {isProfileComplete ? 'Profile Complete!' : 'Complete Your Profile'}
        </Text>
        
        <Text style={styles.message}>{message}</Text>
        
        {!isProfileComplete && (
          <>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${completionPercentage}%` }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>
                {completionPercentage}% Complete
              </Text>
            </View>
            
            {nextSteps.length > 0 && (
              <View style={styles.nextStepsContainer}>
                {nextSteps.slice(0, 2).map((step, index) => (
                  <Text key={index} style={styles.nextStepText}>
                    • {step}
                  </Text>
                ))}
              </View>
            )}
          </>
        )}
        
        <View style={styles.actionsContainer}>
          {!isProfileComplete && (
            <TouchableOpacity 
              style={styles.completeButton}
              onPress={handleCompleteProfile}
              activeOpacity={0.8}
            >
              <Text style={styles.completeButtonText}>
                Complete Profile
              </Text>
              <Ionicons 
                name="arrow-forward" 
                size={16} 
                color={getBannerColor()} 
              />
            </TouchableOpacity>
          )}
          
          {dismissible && (
            <TouchableOpacity 
              style={styles.dismissButton}
              onPress={handleDismiss}
              activeOpacity={0.7}
            >
              <Ionicons 
                name="close" 
                size={20} 
                style={styles.dismissIcon}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

export default ProfileCompletionBanner;
