import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { useProfileCompletion } from '@/hooks/useProfileCompletion';

interface FeatureAccessGuardProps {
  feature: 'quotes' | 'policies' | 'claims' | 'payments' | 'documents';
  children: React.ReactNode;
  fallbackComponent?: React.ReactNode;
  showPrompt?: boolean;
}

const FeatureAccessGuard: React.FC<FeatureAccessGuardProps> = ({
  feature,
  children,
  fallbackComponent,
  showPrompt = true
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const { checkFeatureAccess, completionPercentage } = useProfileCompletion();

  const accessResult = checkFeatureAccess(feature);

  // If user can access the feature, render children normally
  if (accessResult.canAccess) {
    return <>{children}</>;
  }

  // If custom fallback is provided, use it
  if (fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  // If showPrompt is false, render children but grayed out
  if (!showPrompt) {
    return (
      <View style={styles.grayedOutContainer}>
        <View style={styles.grayedOutContent}>
          {children}
        </View>
        <View style={styles.grayedOutOverlay} />
      </View>
    );
  }

  // Default: show access denied prompt
  const handleCompleteProfile = () => {
    router.push('/(app)/profile/edit');
  };

  const getFeatureDisplayName = (feature: string) => {
    const names: Record<string, string> = {
      quotes: 'Insurance Quotes',
      policies: 'Policies',
      claims: 'Claims',
      payments: 'Payments',
      documents: 'Documents'
    };
    return names[feature] || feature;
  };

  const getFeatureIcon = (feature: string) => {
    const icons: Record<string, string> = {
      quotes: 'document-text',
      policies: 'shield-checkmark',
      claims: 'medical',
      payments: 'card',
      documents: 'folder'
    };
    return icons[feature] || 'lock-closed';
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
      backgroundColor: colors.background,
    },
    grayedOutContainer: {
      position: 'relative',
    },
    grayedOutContent: {
      opacity: 0.3,
    },
    grayedOutOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'transparent',
    },
    iconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    icon: {
      color: colors.textSecondary,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      textAlign: 'center',
      marginBottom: spacing.md,
    },
    message: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: spacing.lg,
    },
    progressContainer: {
      width: '100%',
      marginBottom: spacing.lg,
    },
    progressLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      textAlign: 'center',
      marginBottom: spacing.sm,
    },
    progressBar: {
      height: 8,
      backgroundColor: colors.surface,
      borderRadius: 4,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: colors.primary,
      borderRadius: 4,
    },
    progressText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.sm,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    primaryButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
    },
    primaryButtonText: {
      fontFamily: typography.fonts.semiBold,
      fontSize: typography.sizes.md,
      color: colors.background,
      marginRight: spacing.sm,
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.border,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderRadius: 8,
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    secondaryButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Ionicons 
          name={getFeatureIcon(feature)} 
          size={40} 
          style={styles.icon}
        />
      </View>
      
      <Text style={styles.title}>
        Complete Your Profile
      </Text>
      
      <Text style={styles.message}>
        {accessResult.reason || `To access ${getFeatureDisplayName(feature)}, please complete your profile first.`}
      </Text>
      
      <View style={styles.progressContainer}>
        <Text style={styles.progressLabel}>
          Profile Completion
        </Text>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${completionPercentage}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {completionPercentage}% Complete
        </Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.primaryButton}
          onPress={handleCompleteProfile}
          activeOpacity={0.8}
        >
          <Text style={styles.primaryButtonText}>
            Complete Profile
          </Text>
          <Ionicons 
            name="arrow-forward" 
            size={20} 
            color={colors.background} 
          />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.secondaryButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.secondaryButtonText}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default FeatureAccessGuard;
