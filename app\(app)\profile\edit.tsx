import React, { useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchProfile } from '@/store/profileSlice';
import HeaderBar from '@/components/navigation/HeaderBar';
import ProfileForm from '@/components/profile/ProfileForm';

export default function EditProfileScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  // Get profile state
  const profile = useAppSelector(state => state.profile.profile);
  const isLoading = useAppSelector(state => state.profile.isLoading);
  const error = useAppSelector(state => state.profile.error);

  // Fetch profile on mount if not already loaded
  useEffect(() => {
    if (!profile && !isLoading) {
      console.log('[EditProfile] Fetching profile data');
      dispatch(fetchProfile());
    }
  }, [profile, isLoading, dispatch]);

  // Handle successful profile update
  const handleSuccess = () => {
    console.log('[EditProfile] Profile updated successfully');
    router.back();
  };

  // Handle cancel
  const handleCancel = () => {
    console.log('[EditProfile] Profile edit cancelled');
    router.back();
  };

  // Show error if profile fetch failed
  useEffect(() => {
    if (error && error.includes('not found')) {
      Alert.alert(
        'Profile Not Found',
        'No profile found. You can create a new profile.',
        [
          { text: 'Cancel', onPress: () => router.back() },
          { text: 'Create Profile', onPress: () => {
            // Clear the error and continue with form (will create new profile)
            console.log('[EditProfile] Creating new profile');
          }}
        ]
      );
    }
  }, [error]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <HeaderBar 
        title="Edit Profile" 
        showBackButton 
        onBackPress={handleCancel} 
      />
      <View style={styles.content}>
        <ProfileForm 
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </View>
    </SafeAreaView>
  );
}
