import apiService from './api';
import { Notification } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';





// Configure Expo Notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Register for push notifications
const registerForPushNotifications = async () => {
  if (!Device.isDevice) {
    console.log('Push notifications are not available in the simulator');
    return null;
  }

  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return null;
    }

    const token = (await Notifications.getExpoPushTokenAsync()).data;
    console.log('Expo push token:', token);

    // Save token to AsyncStorage
    await AsyncStorage.setItem('pushToken', token);

    // Configure for Android
    if (Platform.OS === 'android') {
      Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    return token;
  } catch (error) {
    console.error('Error registering for push notifications:', error);
    return null;
  }
};

// Notification service methods
export const notificationService = {
  // Register for push notifications
  registerForPushNotifications,

  // Get all notifications
  getNotifications: async (): Promise<Notification[]> => {
    try {
      console.log('[NotificationService] Getting notifications from API');
      const response = await apiService.notifications.getNotifications();
      console.log('[NotificationService] Notifications retrieved successfully:', response.length);
      return response;
    } catch (error) {
      console.error('[NotificationService] Error getting notifications:', error);
      throw error;
    }
  },

  // Mark notification as read
  markAsRead: async (id: string): Promise<void> => {
    try {
      console.log('[NotificationService] Marking notification as read:', id);
      await apiService.notifications.markAsRead(id);
      console.log('[NotificationService] Notification marked as read successfully');
    } catch (error) {
      console.error('[NotificationService] Error marking notification as read:', id, error);
      throw error;
    }
  },

  // Mark all notifications as read
  markAllAsRead: async (): Promise<void> => {
    try {
      console.log('[NotificationService] Marking all notifications as read');
      await apiService.notifications.markAllAsRead();
      console.log('[NotificationService] All notifications marked as read successfully');
    } catch (error) {
      console.error('[NotificationService] Error marking all notifications as read:', error);
      throw error;
    }
  },

  // Delete notification
  deleteNotification: async (id: string): Promise<void> => {
    try {
      console.log('[NotificationService] Deleting notification:', id);
      await apiService.notifications.deleteNotification(id);
      console.log('[NotificationService] Notification deleted successfully');
    } catch (error) {
      console.error('[NotificationService] Error deleting notification:', id, error);
      throw error;
    }
  },

  // Send local notification
  sendLocalNotification: async (title: string, body: string, data?: any): Promise<void> => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending local notification:', error);
      throw error;
    }
  },

  // Clear all notifications
  clearAllNotifications: async (): Promise<void> => {
    try {
      console.log('[NotificationService] Clearing all notifications');
      // Note: This endpoint may not exist in the API, implement if needed
      // await apiService.notifications.clearAllNotifications();
      console.log('[NotificationService] All notifications cleared successfully');
    } catch (error) {
      console.error('[NotificationService] Error clearing all notifications:', error);
      throw error;
    }
  }
};

export default notificationService;
